package com.example.controllers;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class MainController {

    @RequestMapping("/home")
    public String home(@RequestParam("color") String color, Model model) {
        model.addAttribute("color", color);
        model.addAttribute("name", "rocky");
        return "home.html";
    }
}
